# ========================================
# 智能驾驶试验管控工具 - 依赖配置文件
# 版本: 2.0
# 更新日期: 2025-07-07
# ========================================

# 核心GUI框架
PyQt5>=5.15.0,<5.16.0
PyQtWebEngine>=5.15.0,<5.16.0

# 数据处理和分析
pandas>=1.3.0,<3.0.0
numpy>=1.20.0,<2.0.0

# Excel文件处理
xlrd>=2.0.1,<3.0.0
openpyxl>=3.1.0,<4.0.0

# 数据可视化
matplotlib>=3.4.2,<4.0.0
pyqtgraph>=0.12.3,<1.0.0
pyecharts>=2.0.0,<3.0.0

# 图像处理
Pillow>=8.3.1,<11.0.0

# PDF生成和报告
weasyprint>=60.0,<62.0
reportlab>=4.0.0,<5.0.0

# 日期时间处理
python-dateutil>=2.8.2,<3.0.0

# 字体处理（weasyprint依赖）
fonttools>=4.0.0,<5.0.0

# 打包工具（开发用）
pyinstaller>=5.13.0,<6.0.0

# 系统工具库（可选）
psutil>=5.8.0,<6.0.0

# 网络功能（可选，用于在线更新等）
requests>=2.25.0,<3.0.0

# ========================================
# 注意事项
# ========================================
# 1. 以下是Python标准库，无需安装：
#    sqlite3, logging, json, os, sys, datetime, tempfile, typing
#
# 2. 版本范围说明：
#    - 使用 >= 确保最低兼容性
#    - 使用 < 避免主版本升级带来的兼容性问题
#
# 3. 离线部署说明：
#    - 所有依赖包应预先下载到 .venv 目录
#    - 确保包含所有传递依赖
#
# ========================================
# 可选依赖（根据需要启用）
# ========================================

# 更多图表类型支持
# plotly>=5.0.0,<6.0.0
# seaborn>=0.11.0,<1.0.0

# 数据库支持扩展
# SQLAlchemy>=1.4.0,<3.0.0
# psycopg2-binary>=2.9.0,<3.0.0

# 开发和调试工具
# pytest>=6.0.0,<8.0.0
# black>=21.0.0,<24.0.0
# flake8>=3.9.0,<7.0.0
# mypy>=0.910,<2.0.0